import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:schnell_luminator/utils/dio_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/constants.dart';

class TelemetryService {
  Future<dynamic> pushValuesToUserTelemetry(ppeImageUrl) async {
    final sharedPreference = await SharedPreferences.getInstance();
    String? userId = sharedPreference.getString("userId");
    log('get stored userId: $userId');
    Dio dio = DioClient.dio;
    try {
      // var decodedLocation = jsonDecode(location);
      final response = await dio.post(
        '$ticketURL/api/plugins/telemetry/USER/$userId/timeseries/ANY',
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json',
            // 'X-Authorization': 'Bearer $token',
          },
        ),
        data: {"ppeImageUrl": ppeImageUrl},
      );
      log(response.data);
      log('ppe Image url uploaded to telemetry response: ${response.statusCode}');
      if (response.statusCode == 200) {
        return "200";
      } else {
        return response.statusCode;
      }
    } catch (e) {
      if (e is DioError) {
        if (e.error == 'Session expired. Please login again.') {
          return 'refresht token expired';
        }
      }
    }
  }
}
