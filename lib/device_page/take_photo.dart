import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/services.dart';
import 'package:image_compression/image_compression.dart';
import 'package:geolocator/geolocator.dart';
import 'package:schnell_luminator/device_page/image_address_append.dart';
import 'package:schnell_luminator/qr_scan_online.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geocoding/geocoding.dart';
import 'package:location/location.dart' as loc;
import 'package:permission_handler/permission_handler.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:animated_hint_textfield/animated_hint_textfield.dart';
import 'package:uuid/uuid.dart';
import '../utils/asset_folder.dart';
import '../utils/constants.dart';
import '../utils/dialog_box.dart';

class TakePictureScreen extends ConsumerStatefulWidget {
  const TakePictureScreen({
    super.key,
    required this.camera,
    required this.clickeventIsFor,
  });

  final CameraDescription camera;
  final String clickeventIsFor;

  @override
  TakePictureScreenState createState() => TakePictureScreenState();
}

class TakePictureScreenState extends ConsumerState<TakePictureScreen> {
  ImageAddressAppender appender = ImageAddressAppender();
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;
  bool servicestatus = false;
  bool haspermission = false;
  late LocationPermission permission;
  late Position position;
  String long = "", lat = "", acc = '';
  double finacc = 10;
  late StreamSubscription<Position> positionStream;
  final container = ProviderContainer();
  checkpermission() async {
    var camerastatus = await Permission.camera.status;
    if (camerastatus.isDenied) {
      Permission.camera.request();
    }
  }

  List<Widget> getSampleImagesBasedOnprocess(clickeventIsFor) {
    switch (clickeventIsFor) {
      case 'addILM': //
        //mapIconPath = imageLocation['ilm']!;
        return [
          Container(
            height: 115,
            width: 100,
            decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).hoverColor)),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image.file(Assets.ilmScannerImage),
            ),
          ),
          Container(
              height: 115,
              width: 100,
              decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).hoverColor)),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Image.file(Assets.ccmsScannerImage),
              )),
        ];
      case 'addILMViaPole':
        // mapIconPath = imageLocation['ilm']!;
        return [
          Container(
            height: 115,
            width: 100,
            decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).hoverColor)),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image.file(Assets.ilmScannerImage),
            ),
          ),
          Container(
              height: 115,
              width: 100,
              decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).hoverColor)),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Image.file(Assets.ccmsScannerImage),
              )),
        ];
      case 'addLamp':
        // mapIconPath = imageLocation['lamp']!;
        return [
          Image.file(Assets.lampSampleImages1),
          Image.file(Assets.lampSampleImages2),
        ];

      case '1':
        // mapIconPath = imageLocation['ilm']!;
        return [
          Container(
            height: 115,
            width: 100,
            decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).hoverColor)),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image.file(Assets.ilmScannerImage),
            ),
          ),
          Container(
              height: 115,
              width: 100,
              decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).hoverColor)),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Image.file(Assets.ccmsScannerImage),
              )),
        ];
      case 'ilmReplace': //
        // mapIconPath = imageLocation['ilm']!;
        return [
          Container(
            height: 115,
            width: 100,
            decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).hoverColor)),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image.file(Assets.ilmScannerImage),
            ),
          ),
          Container(
              height: 115,
              width: 100,
              decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).hoverColor)),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Image.file(Assets.ccmsScannerImage),
              )),
        ];
      case 'lampOnlyReplace': //
        // mapIconPath = imageLocation['lamp']!;
        return [
          Image.file(Assets.lampSampleImages1),
          Image.file(Assets.lampSampleImages2),
        ];
      case 'lamp+ilmReplace': //
        // mapIconPath = imageLocation['lamp']!;
        return [
          Image.file(Assets.lampSampleImages1),
          Image.file(Assets.lampSampleImages2),
        ];
      case 'gwReplace':
        // mapIconPath = imageLocation['gw']!;
        return [
          Container(
            height: 115,
            width: 100,
            decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).hoverColor)),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image.file(Assets.ilmScannerImage),
            ),
          ),
          Container(
              height: 115,
              width: 100,
              decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).hoverColor)),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Image.file(Assets.ccmsScannerImage),
              )),
        ];
      case 'ilmRemove': //
        // mapIconPath = imageLocation['ilm']!;
        return [
          Container(
            height: 115,
            width: 100,
            decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).hoverColor)),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image.file(Assets.ilmScannerImage),
            ),
          ),
          Container(
              height: 115,
              width: 100,
              decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).hoverColor)),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Image.file(Assets.ccmsScannerImage),
              )),
        ];
      case 'scanLampViaILM': //
        // mapIconPath = imageLocation['ilm']!;
        return [
          Container(
            height: 115,
            width: 100,
            decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).hoverColor)),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image.file(Assets.ilmScannerImage),
            ),
          ),
          Container(
              height: 115,
              width: 100,
              decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).hoverColor)),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Image.file(Assets.ccmsScannerImage),
              )),
        ];
      case 'withoutLampViaILM': //
        // mapIconPath = imageLocation['ilm']!;
        return [
          Container(
            height: 115,
            width: 100,
            decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).hoverColor)),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image.file(Assets.ilmScannerImage),
            ),
          ),
          Container(
              height: 115,
              width: 100,
              decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).hoverColor)),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Image.file(Assets.ccmsScannerImage),
              )),
        ];
      case 'removeLamp': //
        // mapIconPath = imageLocation['lamp']!;
        return [
          Image.file(Assets.lampSampleImages1),
          Image.file(Assets.lampSampleImages2),
        ];

      case 'addLampInMaintenance': //
        // mapIconPath = imageLocation['lamp']!;
        return [
          Image.file(Assets.lampSampleImages1),
          Image.file(Assets.lampSampleImages2),
        ];
    }
    return [];
  }

  @override
  void initState() {
    super.initState();
    _controller = CameraController(widget.camera, ResolutionPreset.medium,
        enableAudio: false);
    _initializeControllerFuture = _controller.initialize();
    ref.read(deviceController).getTitleBasedOnActivity(widget.clickeventIsFor);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> images = getSampleImagesBasedOnprocess(widget.clickeventIsFor);
    var qrViewController = ref
        .watch(ref.watch(isSecondTime) ? qrController2 : qrController)
        .qrViewController;
    String poleLocation = ref.watch(deviceController).poleLocation;
    String lpLandmark = ref.watch(deviceController).lpLandmark;
    String firstDeviceType = ref.watch(deviceController).firstDeviceType;
    String ccmsLandmark = ref.watch(deviceController).ccmsLandmark;
    String hubLandmark = ref.watch(deviceController).hubLandmark;

    return SafeArea(
      child: WillPopScope(
        onWillPop: () async {
          Navigator.pop(context);
          if (qrViewController != null) {
            qrViewController.resumeCamera();
          }
          return true;
        },
        child: Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: Theme.of(context).cardColor,
              ),
              onPressed: () {
                Navigator.pop(context);
                if (qrViewController != null) {
                  qrViewController.resumeCamera();
                }
              },
            ),
            title: Text(
              'Take a picture',
              style: TextStyle(color: Theme.of(context).cardColor),
            ),
            backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
          ),
          body: Container(
            color: Theme.of(context).dialogTheme.backgroundColor,
            child: Column(children: [
              Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ...images,
                    ],
                  )),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.only(
                  left: 15.0,
                  right: 15.0,
                ),
                child: FutureBuilder<void>(
                  future: _initializeControllerFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      return CameraPreview(_controller);
                    } else {
                      return const Center(child: CircularProgressIndicator());
                    }
                  },
                ),
              ),
            ]),
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: Padding(
            padding: const EdgeInsets.all(15.0),
            child: SizedBox(
              height: 70.0,
              width: 70.0,
              child: FittedBox(
                child: FloatingActionButton(
                  backgroundColor: Theme.of(context).cardColor,
                  onPressed: () async {
                    EasyLoading.show(status: 'loading...', dismissOnTap: false);
                    try {
                      await _initializeControllerFuture;
                      final image = await _controller.takePicture();
                      _controller.pausePreview();

                      loc.Location location = loc.Location();

                      bool serviceEnabled;
                      loc.PermissionStatus permissionGranted;
                      loc.LocationData locationData;

                      serviceEnabled = await location.serviceEnabled();
                      if (!serviceEnabled) {
                        serviceEnabled = await location.requestService();
                        if (!serviceEnabled) {
                          return;
                        }
                      }

                      Future<String> getUserLocation(lat, long) async {
                        List<Placemark> placemarks =
                            await placemarkFromCoordinates(lat, long);
                        Placemark place = placemarks[0];
                        // Create a list to hold non-empty location components
                        List<String> locationComponents = [];

                        // Add non-empty components to the list
                        if (place.subLocality != null &&
                            place.subLocality!.isNotEmpty) {
                          locationComponents.add(place.subLocality!);
                        }
                        if (place.locality != null &&
                            place.locality!.isNotEmpty) {
                          locationComponents.add(place.locality!);
                        }
                        if (place.thoroughfare != null &&
                            place.thoroughfare!.isNotEmpty) {
                          locationComponents.add(place.thoroughfare!);
                        }
                        if (place.administrativeArea != null &&
                            place.administrativeArea!.isNotEmpty) {
                          locationComponents.add(place.administrativeArea!);
                        }
                        if (place.country != null &&
                            place.country!.isNotEmpty) {
                          locationComponents.add(place.country!);
                        }
                        if (place.postalCode != null &&
                            place.postalCode!.isNotEmpty) {
                          locationComponents.add(place.postalCode!);
                        }

                        // Join the non-empty components into a single string
                        return locationComponents.join(', ');
                        // return '${place.subLocality}, ${place.locality}, ${place.thoroughfare}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
                      }

                      String landMark = '';

                      if (firstDeviceType == "GW") {
                        if (ccmsLandmark.isEmpty) {
                          if (hubLandmark.isEmpty) {
                            locationData = await location.getLocation();
                            double lat = locationData.latitude!;
                            double long = locationData.longitude!;
                            landMark = await getUserLocation(lat, long);
                          } else {
                            landMark = hubLandmark;
                          }
                        } else {
                          landMark = ccmsLandmark;
                        }
                      } else {
                        if (poleLocation.isEmpty) {
                          if (lpLandmark.isEmpty) {
                            locationData = await location.getLocation();
                            double lat = locationData.latitude!;
                            double long = locationData.longitude!;
                            landMark = await getUserLocation(lat, long);
                          } else {
                            landMark = lpLandmark;
                          }
                        } else {
                          landMark = poleLocation;
                        }
                      }
                      // String landMark = firstDeviceType == "GW"
                      //     ? ccmsLandmark.isEmpty
                      //         ? (hubLandmark.isEmpty
                      //             ? await getUserLocation(lat, long)
                      //             : hubLandmark)
                      //         : ccmsLandmark
                      //     : poleLocation.isEmpty
                      //         ? (lpLandmark.isEmpty
                      //             ? await getUserLocation(lat, long)
                      //             : lpLandmark)
                      //         : poleLocation;

                      if (!mounted) return;
                      EasyLoading.dismiss();
                      _controller.pausePreview();
                      if (context.mounted) {
                        await Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => DisplayPictureScreen(
                              imagePath: image.path,
                              landMark: landMark,
                            ),
                          ),
                        );
                      }
                    } catch (e) {
                      EasyLoading.dismiss();
                      if (context.mounted) {
                        Navigator.of(context).pushNamedAndRemoveUntil(
                            homeRoute, (Route<dynamic> route) => false);
                      }
                      if (context.mounted) {
                        await snackBar(context, ErrorMessages.networkErrorTitle,
                            ErrorMessages.networkErrorMessage);
                      }

                      log(e.toString());
                    }
                  },
                  child: const Icon(Icons.camera_alt),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class DisplayPictureScreen extends ConsumerWidget {
  final String imagePath;
  final String landMark;

  final TextEditingController locationAddress = TextEditingController();
  final TextEditingController newMeterReadingValue = TextEditingController();
  final TextEditingController oldMeterReadingValue = TextEditingController();

  DisplayPictureScreen({
    super.key,
    required this.imagePath,
    required this.landMark,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var deviceType = ref.watch(deviceController).firstDeviceType;
    String lampAssetType = ref.watch(deviceController).lampAssetType;
    String clickeventIsFor = ref.watch(deviceController).clickeventIsFor;
    bool isButtonEnabled = ref.watch(deviceController).isButtonEnabled;
    int firstOrSecondDevice = ref.watch(deviceController).firstOrSecondDevice;
    String fetchedLong = ref.watch(deviceController).fetchedLong;
    String fetchedLat = ref.watch(deviceController).fetchedLat;
    String ccmsLat = ref.watch(deviceController).ccmsLat;
    String ccmsLong = ref.watch(deviceController).ccmsLong;
    String hubLat = ref.watch(deviceController).hubLat;
    String hubLong = ref.watch(deviceController).hubLong;
    String lpLat = ref.watch(deviceController).lpLat;
    String lpLong = ref.watch(deviceController).lpLong;
    String poleLat = ref.watch(deviceController).poleLat;
    String poleLong = ref.watch(deviceController).poleLong;
    bool isLocationFetched = ref.watch(deviceController).isLocationFetched;
    ImageAddressAppender appender = ImageAddressAppender();
    String region = ref.watch(deviceController).selectedRegion;
    String zone = ref.watch(deviceController).selectedZone;
    String ward = ref.watch(deviceController).selectedWard;
    String lpRegion = ref.watch(deviceController).lpregion;
    String lpzone = ref.watch(deviceController).lpzone;
    String lpward = ref.watch(deviceController).lpward;
    String deviceNameForImgAppend =
        ref.watch(deviceController).deviceNameForImgAppend;
    File mapIconPath = ref.watch(deviceController).mapIconPath;

    String getActivity() {
      if (clickeventIsFor == 'addILM' ||
          clickeventIsFor == 'addILMViaPole' ||
          clickeventIsFor == 'scanLampViaILM' ||
          clickeventIsFor == 'withoutLampViaILM' ||
          clickeventIsFor == 'addLamp' ||
          clickeventIsFor == '1') {
        return 'INSTALL';
      } else if (clickeventIsFor == 'ilmReplace' ||
          clickeventIsFor == 'lampOnlyReplace' ||
          clickeventIsFor == 'lamp+ilmReplace' ||
          clickeventIsFor == 'ebMeterReplace' ||
          clickeventIsFor == 'gwReplace') {
        return 'REPLACE';
      } else if (clickeventIsFor == 'ilmRemove' ||
          clickeventIsFor == 'removeLamp') {
        return 'REMOVE';
      }
      return '';
    }

    return SafeArea(
      child: WillPopScope(
        onWillPop: () async => false,
        child: Scaffold(
          extendBody: false,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 60,
                child: FittedBox(
                  child: FloatingActionButton(
                    heroTag: const Text("btn2"),
                    backgroundColor: Theme.of(context).primaryColor,
                    onPressed: () async {
                      final cameras = await availableCameras();
                      final firstCamera = cameras.first;
                      if (context.mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TakePictureScreen(
                              camera: firstCamera,
                              clickeventIsFor: clickeventIsFor,
                            ),
                          ),
                        );
                      }
                    },
                    child: const Icon(
                      Icons.restart_alt,
                      size: 26,
                    ),
                  ),
                ),
              ),
              const SizedBox(
                width: 20,
              ),
              SizedBox(
                height: 70,
                child: FittedBox(
                  child: ElevatedButton.icon(
                    icon: const Icon(
                      Icons.check,
                      size: 20,
                    ),
                    label: const Text('Complete'),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: isButtonEnabled
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).dialogTheme.backgroundColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50.0),
                      ),
                    ),
                    onPressed: isButtonEnabled
                        ? () async {
                            SystemChannels.textInput
                                .invokeMethod('TextInput.hide');

                            // validates whether the location is fetched
                            if (!isLocationFetched &&
                                (clickeventIsFor == "1" ||
                                    clickeventIsFor == "withoutLampViaILM" ||
                                    clickeventIsFor == "scanLampViaILM")) {
                              await warningAlert(ref, context);
                              return;
                            }
                            ref.read(deviceController).disableButton();
                            EasyLoading.show(
                                status: 'loading...', dismissOnTap: false);

                            Utility.isConnected().then((value) async {
                              if (value) {
                                var uuid = const Uuid();
                                var manualEntered = locationAddress.text;
                                if (context.mounted) {
                                  await ref
                                      .read(deviceController)
                                      .addUserInputValue(
                                          ref,
                                          context,
                                          manualEntered,
                                          newMeterReadingValue.text,
                                          oldMeterReadingValue.text);
                                }
                                String manualEnteredAddress =
                                    manualEntered.isEmpty
                                        ? ''
                                        : '$manualEntered, ';
                                String projectContext = lpRegion.isEmpty
                                    ? '$region / $zone / $ward'
                                    : '$lpRegion / $lpzone / $lpward';
                                File file = mapIconPath;
                                Uint8List iconData = file.readAsBytesSync();

                                await appender.appendAddressToImage(
                                  ref,
                                  imagePath,
                                  manualEnteredAddress,
                                  landMark,
                                  iconData,
                                  projectContext,
                                  deviceNameForImgAppend,
                                );

                                File imageFile = File(imagePath);
                                ImageFile inputImageFile = ImageFile(
                                  rawBytes: imageFile.readAsBytesSync(),
                                  filePath: imagePath,
                                );
                                ImageFileConfiguration input =
                                    ImageFileConfiguration(
                                        config:
                                            const Configuration(jpgQuality: 90),
                                        input: inputImageFile);
                                final compressedImage = compress(
                                  input,
                                );
                                var fileName = uuid.v1();
                                String deviceImage =
                                    base64Encode(compressedImage.rawBytes);
                                log('captured device image: $deviceImage');
                                log('generated uid: $fileName');
                                if (context.mounted) {
                                  ref.read(deviceController).s3imageUpload(
                                      ref,
                                      context,
                                      deviceImage,
                                      firstOrSecondDevice,
                                      fileName,
                                      getActivity());
                                }
                                var luminareData = {
                                  "deviceImage": imagePath,
                                  "landmark": landMark
                                };
                                var gwdata = {
                                  "deviceImage": imagePath,
                                  "location": landMark
                                };

                                //installation
                                if (firstOrSecondDevice == 1) {
                                  //addLamp
                                  if (clickeventIsFor == "addLamp" ||
                                      clickeventIsFor ==
                                          "addLampInMaintenance") {
                                    if (context.mounted) {
                                      await ref
                                          .read(deviceController)
                                          .lampInstallation(ref, context,
                                              luminareData, clickeventIsFor);
                                    }
                                  } else {
                                    if (context.mounted) {
                                      await ref
                                          .read(deviceController)
                                          .deviceInstallation(
                                              ref,
                                              context,
                                              deviceType == "ILM" ||
                                                      deviceType ==
                                                          "LUMINODE" ||
                                                      deviceType == 'ILM-4G' ||
                                                      lampAssetType == "LAMP"
                                                  ? luminareData
                                                  : gwdata,
                                              clickeventIsFor);
                                    }
                                  }
                                  //replace
                                } else if (firstOrSecondDevice == 2) {
                                  if (clickeventIsFor == "lampOnlyReplace") {
                                    if (context.mounted) {
                                      await ref
                                          .read(deviceController)
                                          .lampOnlyReplace(
                                              ref, context, luminareData);
                                    }
                                  } else if (clickeventIsFor ==
                                      "lamp+ilmReplace") {
                                    if (context.mounted) {
                                      await ref
                                          .read(deviceController)
                                          .lampAndILMReplace(
                                              ref, context, luminareData);
                                    }
                                  } else {
                                    if (context.mounted) {
                                      await ref
                                          .read(deviceController)
                                          .deviceReplace(
                                              ref,
                                              context,
                                              deviceType == "ILM" ||
                                                      deviceType ==
                                                          "LUMINODE" ||
                                                      deviceType == 'ILM-4G'
                                                  ? luminareData
                                                  : gwdata);
                                    }
                                  }
                                  //remove
                                } else {
                                  //removeLamp
                                  if (clickeventIsFor == "removeLamp") {
                                    if (context.mounted) {
                                      await ref
                                          .read(deviceController)
                                          .lampRemove(ref, context, {});
                                    }
                                  } else {
                                    if (context.mounted) {
                                      await ref
                                          .read(deviceController)
                                          .deviceRemove(ref, context, {});
                                    }
                                  }
                                }
                                // }
                              } else {
                                if (context.mounted) {
                                  await snackBar(
                                      context,
                                      ErrorMessages.offlineErrorTitle,
                                      ErrorMessages.offlineErrorMessage);
                                }
                              }
                            });
                          }
                        : null,
                    onLongPress: null, // Disable long press
                  ),
                ),
              ),
            ],
          ),
          appBar: AppBar(
            automaticallyImplyLeading: false,
            centerTitle: true,
            title: SizedBox(
              // color: Theme.of(context).canvasColor,
              width: MediaQuery.of(context).size.width / 1,
              child: Center(
                child: Text(
                  ref
                      .read(deviceController)
                      .getTitleBasedOnActivity(clickeventIsFor),
                  style: const TextStyle(
                      color: Color.fromARGB(248, 32, 61, 78), fontSize: 13),
                ),
              ),
            ),
            backgroundColor: Theme.of(context).dialogTheme.backgroundColor,
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context)
                      .viewInsets
                      .bottom), // Push content up when keyboard appears

              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 15.0),
                    child: Container(
                        color: Theme.of(context).indicatorColor,
                        height: MediaQuery.of(context).size.height / 1.85,
                        child: Image.file(File(imagePath))),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.only(top: 15.0, right: 5.0, left: 5.0),
                    child: Column(
                      children: [
                        if (clickeventIsFor == 'ebMeterReplace')
                          Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: oldMeterReadingValue,
                                  keyboardType: TextInputType.number,
                                  maxLength: 5,
                                  style: TextStyle(
                                      fontSize: 12,
                                      overflow: TextOverflow.clip,
                                      color: Theme.of(context)
                                          .secondaryHeaderColor),
                                  decoration: InputDecoration(
                                      labelText: 'Old Meter Reading',
                                      labelStyle: TextStyle(
                                        color: Theme.of(context)
                                            .secondaryHeaderColor,
                                        fontSize: 13,
                                      ),
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      counterText: '',
                                      prefixIcon: Transform.scale(
                                        scale: 0.5,
                                        child: Image.file(Assets.meterReading),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.all(16.0),
                                      filled: true,
                                      fillColor: Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.20),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: const BorderSide(
                                            color: Color.fromARGB(
                                                248, 64, 124, 161),
                                            width: 1.0),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color:
                                                Theme.of(context).primaryColor,
                                            width: 1.0),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      )),
                                ),
                              ),
                              const SizedBox(
                                width: 8,
                              ),
                              Expanded(
                                child: TextField(
                                  controller: newMeterReadingValue,
                                  keyboardType: TextInputType.number,
                                  maxLength: 5,
                                  style: TextStyle(
                                      fontSize: 12,
                                      overflow: TextOverflow.clip,
                                      color: Theme.of(context)
                                          .secondaryHeaderColor),
                                  decoration: InputDecoration(
                                      labelText: 'New Meter Reading',
                                      labelStyle: TextStyle(
                                        color: Theme.of(context)
                                            .secondaryHeaderColor,
                                        fontSize: 13,
                                      ),
                                      counterText: '',
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      prefixIcon: Transform.scale(
                                        scale: 0.5,
                                        child: Image.file(Assets.meterReading),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.all(16.0),
                                      filled: true,
                                      fillColor: Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.20),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: const BorderSide(
                                            color: Color.fromARGB(
                                                248, 64, 124, 161),
                                            width: 1.0),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color:
                                                Theme.of(context).primaryColor,
                                            width: 1.0),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      )),
                                ),
                              ),
                            ],
                          ),
                        if (clickeventIsFor == 'ebMeterReplace')
                          const SizedBox(
                            height: 8,
                          ),
                        AnimatedTextField(
                          animationType: Animationtype.typer,
                          keyboardType: TextInputType
                              .visiblePassword, // to remove emoji from the keyboard
                          maxLength: 100,
                          controller: locationAddress,
                          hintTexts: const [
                            'Provide additional landmark information if any',
                          ],
                          style: TextStyle(
                              color: Theme.of(context).secondaryHeaderColor),
                          hintTextStyle: TextStyle(
                              fontSize: 13,
                              color: Theme.of(context)
                                  .secondaryHeaderColor
                                  .withOpacity(0.8)),

                          decoration: InputDecoration(
                              counterText: '',
                              prefixIcon: Icon(Icons.location_on_outlined,
                                  color: Theme.of(context)
                                      .secondaryHeaderColor
                                      .withOpacity(0.8)),
                              contentPadding: const EdgeInsets.all(16.0),
                              filled: true,
                              fillColor: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.20),
                              focusedBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                    color: Color.fromARGB(248, 64, 124, 161),
                                    width: 1.0),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                    color: Theme.of(context).primaryColor,
                                    width: 1.0),
                                borderRadius: BorderRadius.circular(8.0),
                              )),
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        !isLocationFetched && clickeventIsFor == "1"
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    height: 15.0,
                                    width: 15.0,
                                    child: CircularProgressIndicator(
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 7,
                                  ),
                                  Text(
                                    'Fetching Location...',
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).primaryColor),
                                  ),
                                ],
                              )
                            : Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  landMark.isNotEmpty
                                      ? Text(
                                          landMark,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        )
                                      : Container(),
                                  const SizedBox(
                                    height: 2,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      const Text(
                                        'Latitude : ',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        deviceType == "GW"
                                            ? ccmsLat.isEmpty
                                                ? (hubLat.isEmpty
                                                    ? fetchedLat
                                                    : hubLat)
                                                : ccmsLat
                                            : poleLat.isEmpty
                                                ? (lpLat.isEmpty
                                                    ? fetchedLat
                                                    : lpLat)
                                                : poleLat,
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const Text(
                                        'Longitude : ',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        deviceType == "GW"
                                            ? ccmsLong.isEmpty
                                                ? (hubLong.isEmpty
                                                    ? fetchedLong
                                                    : hubLong)
                                                : ccmsLong
                                            : poleLong.isEmpty
                                                ? (lpLong.isEmpty
                                                    ? fetchedLong
                                                    : lpLong)
                                                : poleLong,
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  )
                                ],
                              )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
