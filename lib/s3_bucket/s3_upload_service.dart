import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:schnell_luminator/device_page/telemetry_service.dart';

import 's3_config.dart';

class S3UploadService {
  final TelemetryService _telemetryService = TelemetryService();

  Future<String> updateSurveyImageServiceS3(
      String base64Image, String fileName, BuildContext context,
      {bool isPPE = false}) async {
    try {
      log('Starting S3 upload for file: $fileName');

      final result = await uploadImageToS3(
        base64Image: base64Image,
        fileName: fileName,
        folder: isPPE ? S3Config.ppeImageFolder : S3Config.luminatorFolder,
        contentType: 'image/jpeg',
      );

      log('S3 upload result: $result');

      // Check if upload was successful
      if (result != null && result.isNotEmpty) {
        log('S3 upload successful, URL: $result');
        var status = isPPE
            ? await _telemetryService.pushValuesToUserTelemetry(result)
            : '200';
        return status;
      } else {
        log('S3 upload failed - result is null or empty');
        return "400";
      }
    } catch (e) {
      log('S3 upload error: $e');
      log('S3 upload error type: ${e.runtimeType}');
      return 'Something Went Wrong';
    }
  }

  /// Upload image directly to S3 using presigned URL approach
  static Future<String?> uploadImageToS3({
    required String base64Image,
    required String fileName,
    required String folder,
    String contentType = 'application/octet-stream',
  }) async {
    try {
      log('Starting S3 upload for file: $fileName');

      // Convert base64 to bytes - Clean the base64 string first
      String cleanBase64 = base64Image.trim();
      // Remove data URL prefix if present
      if (cleanBase64.startsWith('data:')) {
        final commaIndex = cleanBase64.indexOf(',');
        if (commaIndex != -1) {
          cleanBase64 = cleanBase64.substring(commaIndex + 1);
        }
      }

      final decodedBytes = base64Decode(cleanBase64);
      // Ensure we have Uint8List for consistent handling
      final bytes = Uint8List.fromList(decodedBytes);
      log('Base64 decoded successfully, bytes length: ${bytes.length}');

      if (bytes.isEmpty) {
        log('Error: Image bytes are empty');
        return null;
      }

      // Log basic file info
      log('File size: ${bytes.length} bytes');

      // Create the S3 key (path)
      final key = '$folder/$fileName';
      log('S3 Key: $key');

      // Upload using HttpClient
      final result = await _uploadWithHttpClient(
        bytes: bytes,
        key: key,
        contentType: contentType,
      );

      if (result != null) {
        log('S3 upload successful, URL: $result');
        return result;
      } else {
        log('S3 upload failed');
        return null;
      }
    } catch (e) {
      log('S3 upload error: $e');
      return null;
    }
  }

  /// Create AWS Signature Version 4 authorization header
  static String _createAuthorizationHeader({
    required String method,
    required String uri,
    required Map<String, String> headers,
    required String dateStamp,
    required String timeStamp,
    required String payloadHash,
  }) {
    // Step 1: Create canonical request
    final canonicalHeaders = headers.entries
        .map((e) => '${e.key.toLowerCase()}:${e.value.trim()}')
        .toList()
      ..sort();

    final signedHeaders = headers.keys.map((k) => k.toLowerCase()).toList()
      ..sort();

    final canonicalRequest = [
      method,
      uri,
      '', // query string
      '${canonicalHeaders.join('\n')}\n',
      signedHeaders.join(';'),
      payloadHash,
    ].join('\n');

    // Step 2: Create string to sign
    final credentialScope = '$dateStamp/${S3Config.region}/s3/aws4_request';
    final stringToSign = [
      'AWS4-HMAC-SHA256',
      timeStamp,
      credentialScope,
      _sha256Hash(utf8.encode(canonicalRequest)),
    ].join('\n');

    // Step 3: Calculate signature
    final signature = _calculateSignature(
      stringToSign: stringToSign,
      dateStamp: dateStamp,
    );

    // Step 4: Create authorization header
    return 'AWS4-HMAC-SHA256 '
        'Credential=${S3Config.accessKey}/$credentialScope, '
        'SignedHeaders=${signedHeaders.join(';')}, '
        'Signature=$signature';
  }

  /// Calculate AWS Signature Version 4 signature
  static String _calculateSignature({
    required String stringToSign,
    required String dateStamp,
  }) {
    final kDate =
        _hmacSha256(utf8.encode('AWS4${S3Config.secretKey}'), dateStamp);
    final kRegion = _hmacSha256(kDate, S3Config.region);
    final kService = _hmacSha256(kRegion, 's3');
    final kSigning = _hmacSha256(kService, 'aws4_request');
    final signature = _hmacSha256(kSigning, stringToSign);

    return signature.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
  }

  /// HMAC-SHA256 helper
  static List<int> _hmacSha256(List<int> key, String data) {
    final hmac = Hmac(sha256, key);
    return hmac.convert(utf8.encode(data)).bytes;
  }

  /// SHA256 hash helper
  static String _sha256Hash(List<int> data) {
    return sha256.convert(data).toString();
  }

  /// Format date for AWS (YYYYMMDD)
  static String _formatDate(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}'
        '${date.month.toString().padLeft(2, '0')}'
        '${date.day.toString().padLeft(2, '0')}';
  }

  /// Format datetime for AWS (YYYYMMDDTHHMMSSZ)
  static String _formatDateTime(DateTime date) {
    return '${_formatDate(date)}T'
        '${date.hour.toString().padLeft(2, '0')}'
        '${date.minute.toString().padLeft(2, '0')}'
        '${date.second.toString().padLeft(2, '0')}Z';
  }

  static Future<String?> _uploadWithHttpClient({
    required Uint8List bytes,
    required String key,
    required String contentType,
  }) async {
    try {
      log('Starting S3 upload using HttpClient...');

      final now = DateTime.now().toUtc();
      final dateStamp = _formatDate(now);
      final timeStamp = _formatDateTime(now);

      final uri = Uri.parse(
          'https://${S3Config.bucketName}.s3.${S3Config.region}.amazonaws.com/$key');

      // Create headers for signing
      final headersForSigning = <String, String>{
        'Host': '${S3Config.bucketName}.s3.${S3Config.region}.amazonaws.com',
        'x-amz-date': timeStamp,
        'x-amz-content-sha256': _sha256Hash(bytes),
      };

      final authHeader = _createAuthorizationHeader(
        method: 'PUT',
        uri: '/$key',
        headers: headersForSigning,
        dateStamp: dateStamp,
        timeStamp: timeStamp,
        payloadHash: _sha256Hash(bytes),
      );

      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 30);

      try {
        final request = await client.putUrl(uri);

        request.headers.set('Host',
            '${S3Config.bucketName}.s3.${S3Config.region}.amazonaws.com');
        request.headers.set('x-amz-date', timeStamp);
        request.headers.set('x-amz-content-sha256', _sha256Hash(bytes));
        request.headers.set('Authorization', authHeader);
        request.headers.set('Content-Type', contentType);
        request.headers.set('Content-Length', bytes.length.toString());

        log('Uploading ${bytes.length} bytes to S3...');

        request.add(bytes);

        final response = await request.close();
        final statusCode = response.statusCode;

        if (statusCode == 200 || statusCode == 204) {
          log('S3 upload successful - Status: $statusCode');
          return uri.toString();
        } else {
          final responseBody = await response.transform(utf8.decoder).join();
          log('S3 upload failed - Status: $statusCode');
          log('Error response: $responseBody');
          return null;
        }
      } finally {
        client.close();
      }
    } catch (e) {
      log('S3 upload error: $e');
      return null;
    }
  }
}
