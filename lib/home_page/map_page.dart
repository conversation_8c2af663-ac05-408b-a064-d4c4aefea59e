import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_location_marker/flutter_map_location_marker.dart';
import 'package:flutter_map_marker_cluster/flutter_map_marker_cluster.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:schnell_luminator/location_selection/location_controller.dart';

class MapPage extends ConsumerStatefulWidget {
  const MapPage({super.key});

  @override
  ConsumerState<MapPage> createState() => _MapPageState();
}

class _MapPageState extends ConsumerState<MapPage> {
  @override
  Widget build(BuildContext context) {
    final List<Marker> markers = ref.watch(deviceController).markers;
    final LatLng? userLocation = ref.watch(locationController).userLocation;

    return Scaffold(
      body: FlutterMap(
        options: MapOptions(
          initialCenter: userLocation!,
          initialZoom: 18.0,
          maxZoom: 30,
          backgroundColor: Theme.of(context).canvasColor,
          interactionOptions: const InteractionOptions(
              flags: InteractiveFlag.pinchZoom | InteractiveFlag.drag),
        ),
        children: <Widget>[
          TileLayer(
            maxZoom: 25,
            urlTemplate:
                'https://{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png',
            subdomains: const ['a', 'b', 'c'],
            userAgentPackageName: 'com.example.app',
          ),
          MarkerClusterLayerWidget(
            options: MarkerClusterLayerOptions(
              zoomToBoundsOnClick: false,
              markers: markers,
              maxClusterRadius: 300,
              spiderfyCircleRadius: 50,
              disableClusteringAtZoom: 35,
              size: const Size(50, 50),
              polygonOptions: PolygonOptions(
                borderColor: Theme.of(context).focusColor,
                color: Theme.of(context).secondaryHeaderColor,
                borderStrokeWidth: 3,
              ),
              builder: (context, markers) {
                return Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Theme.of(context).dividerColor,
                    shape: BoxShape.circle,
                  ),
                  child: Text('${markers.length}'),
                );
              },
            ),
          ),
          CurrentLocationLayer(
            alignPositionOnUpdate: AlignOnUpdate.always,
            alignDirectionOnUpdate: AlignOnUpdate.never,
            style: LocationMarkerStyle(
                marker: DefaultLocationMarker(
                  child: Icon(
                    Icons.person,
                    color: Theme.of(context).canvasColor,
                    size: 14,
                  ),
                ),
                markerSize: const Size.square(22),
                showAccuracyCircle: false,
                markerDirection: MarkerDirection.heading),
          ),
        ],
      ),
    );
  }
}

class MarkerLabel extends StatelessWidget {
  final String label;
  final Color bgColor;

  const MarkerLabel({super.key, required this.label, required this.bgColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 20,
      width: 100,
      padding: const EdgeInsets.all(2.0),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Center(
        child: Text(
          label,
          style: TextStyle(
            color: Theme.of(context).secondaryHeaderColor,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}
