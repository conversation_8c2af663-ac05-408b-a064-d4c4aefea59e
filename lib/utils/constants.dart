import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path_provider/path_provider.dart';
import 'package:schnell_luminator/utils/asset_folder.dart';

const appVersion = '3.10.3';
const splashRoute = '/';
const loginRoute = '/login';
const deviceScanRoute = '/deviceScan';
const homeRoute = '/home';
const regionRoute = '/region';
const customerRoute = '/customer';
const lampDetailsRoute = '/lampDetailsRoute';
const poleDetailsRoute = '/poleDetailsRoute';
const poleScanRoute = '/poleScanRoute';
const ticketListRoute = '/ticketListRoute';
const searchRoute = '/searchRoute';
const zoneRoute = '/zone';
const wardRoute = '/ward';
const mapRoute = '/map';
const updateRoute = '/update';
const Color orange = Color.fromARGB(255, 221, 94, 40);
const Color darkYellow = Color.fromARGB(255, 185, 130, 41);
const Color warningColor = Color.fromARGB(255, 171, 171, 0); //WARNING

String appName = 'Luminator';

String baseURL = 'https://api.iotpro.io';
String ticketURL = 'https://iotpro.io';
String webviewDashboardId = '26abf9f0-a246-11ef-8001-693c9082cff6';

// String baseURL = 'https://api.schnelliot.in';
// String ticketURL = 'https://schnelliot.in';
// String webviewDashboardId = '3b987300-ad6a-11ef-98d3-e3bb640e3ce9';

// String baseURL = 'https://beta.api.schnelliot.in';
// String ticketURL = 'https://schnelliot.in';
// String webviewDashboardId = '3b987300-ad6a-11ef-98d3-e3bb640e3ce9';

const secondaryColor = Color.fromARGB(248, 64, 124, 161);
const bgColor = Color.fromARGB(248, 64, 124, 161);
const primaryColor = Color(0xFF2697FF);

const defaultPadding = 16.0;

const logoImage = 'assets/images/logo.png';

const componenetsChanged = {
  "CCMS": [
    "Components Changed",
    "SMPS",
    "Gateway",
    "Meter Cable",
    "Wire",
    "Meter",
    "Timer",
    "MCB",
    "MCB Resetter",
    "Bypass switch",
    "Load Break switch",
    "Connector",
    "Door Switch",
    "SIM",
    "Antenna"
  ],
  "LAMP": ["Components Changed", "Wire", "Lamp", "SPD"],
  "GW": ["Components Changed", "Gateway", "SIM", "Antenna", "Wire"],
  "ILM": ["Components Changed", "ILM", "Wire"],
  "ILM-4G": ["Components Changed", "ILM-4G", "Wire"],
  "LIGHTPOINT": ["Components Changed", "ILM", "Wire", "Lamp", "SPD"],
  "POLE": ["Components Changed", "Pole"]
};

const issuesIdentified = {
  "CCMS": [
    "Issues Identified",
    "SMPS",
    "Gateway",
    "Meter",
    "Wire",
    "Energy Meter",
    "Timer",
    "MCB",
    "MCB Resetter",
    "Bypass switch",
    "Load Break switch",
    "Connector",
    "Door Switch",
    "SIM",
    "Antenna",
    "Input power issue",
    "Circuit or Jumper Fault",
    "Device Reset",
    "No Issue",
    "I dont know"
  ],
  "LAMP": [
    "Issues Identified",
    "Wire",
    "Lamp",
    "SPD",
    "Input Power Issue",
    "Circuit or Jumper Fault",
    "No Issue"
  ],
  "GW": [
    "Issues Identified",
    "Gateway",
    "SIM",
    "Antenna",
    "Wire",
    "Device Reset",
    "I dont know",
    "Input Power Issue",
    "Circuit or Jumper Fault",
    "No Issue"
  ],
  "ILM": [
    "Issues Identified",
    "ILM",
    "Wire",
    "Device Reset",
    "I dont know",
    "Input Power Issue",
    "Circuit or Jumper Fault",
    "No Issue"
  ],
  "ILM-4G": [
    "Issues Identified",
    "ILM-4G",
    "Wire",
    "Device Reset",
    "I dont know",
    "Input Power Issue",
    "Circuit or Jumper Fault",
    "No Issue"
  ],
  "LIGHTPOINT": [
    "Issues Identified",
    "ILM",
    "Lamp",
    "Wire",
    "SPD",
    "Device Reset",
    "I dont know",
    "Input Power Issue",
    "Circuit or Jumper Fault",
    "No Issue",
  ],
  "POLE": ["Issues Identified", "Pole", "No Issue", "I dont know"]
};

const Map<String, List<String>> statusTransitionMap = {
  'OPEN': ['OPEN', 'VISITED & CLOSED', 'VISITED & NOT CLOSED'],
  'ASSIGNED': ['ASSIGNED', 'VISITED & CLOSED', 'VISITED & NOT CLOSED'],
  'CLOSED': ['CLOSED'],
  'VISITED & CLOSED': ['VISITED & CLOSED'],
  'VISITED & NOT CLOSED': ['VISITED & NOT CLOSED', 'VISITED & CLOSED'],
  'VERIFIED & OPENED': ['VERIFIED & OPENED'],
  'VERIFIED & CLOSED': ['VERIFIED & CLOSED'],
};

const problemTypes = {
  "CCMS": [
    "Problem Type*",
    "Lamps not burning",
    "Day time burning",
    "Timing issue",
    "MCB Trip",
    "High Voltage",
    "Low Voltage",
    "High Current",
    "Bypassed",
    "Not communicating",
    "Input Power unavailable",
    "Panel Door Open",
    "Preventive Maintenance",
    "RF Signal Rank poor",
    "4G network poor",
    "Others"
  ],
  "LAMP": [
    "Problem Type*",
    "Not burning",
    "Flickering",
    "Day time burning",
    "Others"
  ],
  "GW": [
    "Problem Type*",
    "Not communicating",
    "Input Power unavailable",
    "RF Signal Rank Poor",
    "4G network poor",
    "Others"
  ],
  "ILM": [
    "Problem Type*",
    "Not communicating",
    "Input Power unavailable",
    "RF Signal Rank poor",
    "4G network poor",
    "Not burning",
    "Flickering",
    "Day time burning",
    "Others"
  ],
  "ILM-4G": [
    "Problem Type*",
    "Not communicating",
    "Input Power unavailable",
    "RF Signal Rank poor",
    "4G network poor",
    "Not burning",
    "Flickering",
    "Day time burning",
    "Others"
  ],
  "POLE": ["Problem Type*", "Pole damaged", "Others"],
  "OTHERS": ["Problem Type*", "Others"],
  "DEVICE/ASSET TYPE*": ["Problem Type*"]
};

Map<String, File> imageLocation = {
  "ilm": Assets.lightpoint,
  "lumiNode": Assets.lightpoint,
  "ilm-4g": Assets.lightpoint,
  "gw": Assets.gateway,
  "nic": Assets.gateway,
  "lightPoint": Assets.lightpoint,
  "ccms": Assets.ccmsPanel,
  "smslc": Assets.ccmsPanel,
  "hub": Assets.hub,
  "lamp": Assets.lampSearch,
  "pole": Assets.pole,
  "eb": Assets.ebMeter,
  "others": Assets.warning
};

Future<bool> getFileDirectory(String fileName) async {
  final dir = await getApplicationDocumentsDirectory();
  File file = File('${dir.path}/$fileName');
  if (!await file.exists()) {
    log('Missing file: $fileName');
    return false;
  }
  return true;
}

const spinkit = SpinKitThreeBounce(
  color: Color.fromARGB(248, 64, 124, 161),
  size: 30.0,
);

const loginLoader = SpinKitRing(
  color: Color.fromARGB(248, 64, 124, 161),
  size: 30.0,
);

ThemeData lightTheme(context) => ThemeData(
      useMaterial3: false,
      scaffoldBackgroundColor: Theme.of(context).canvasColor,
      textTheme:
          GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme.copyWith(
                headlineSmall: TextStyle(color: Colors.grey[800]),
                titleLarge:
                    const TextStyle(color: Color.fromARGB(248, 64, 124, 161)),
                bodyLarge: const TextStyle(color: Colors.grey),
                bodySmall: const TextStyle(color: Colors.black),
                titleMedium: TextStyle(color: Theme.of(context).primaryColor),
                titleSmall: TextStyle(color: Colors.grey[200]),
                bodyMedium: const TextStyle(color: Colors.black),
                labelSmall:
                    const TextStyle(color: Color.fromARGB(248, 64, 124, 161)),
                displaySmall:
                    const TextStyle(color: Color.fromARGB(248, 64, 124, 161)),
              )),
      canvasColor: Colors.white,
      primaryColor: const Color.fromARGB(248, 64, 124, 161),
      cardColor: const Color.fromARGB(248, 32, 61, 78),
      secondaryHeaderColor: Colors.black,
      hoverColor: Colors.blueGrey,
      unselectedWidgetColor: Colors.grey,
      indicatorColor: Colors.red, //error color
      focusColor: Colors.blueAccent[400],
      hintColor: const Color.fromARGB(255, 78, 153, 96), //INDETERMINATE
      // splashColor: const Color.fromARGB(255, 171, 171, 0), //WARNING
      dividerColor: Colors.orange, //MAJOR
      disabledColor: const Color.fromARGB(255, 255, 202, 61),
      dialogTheme: const DialogThemeData(
          backgroundColor: Color.fromARGB(248, 217, 232, 243)), //MINOR
      // shadowColor: const Color.fromARGB(255, 221, 94, 40),
      // highlightColor: const Color.fromARGB(255, 185, 130, 41),
    );

ThemeData darkTheme(context) => ThemeData(
      useMaterial3: false,
      scaffoldBackgroundColor: bgColor,
      textTheme:
          GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme.copyWith(
                headlineSmall:
                    const TextStyle(color: Color.fromARGB(255, 238, 232, 232)),
                titleLarge: const TextStyle(color: Colors.grey),
                bodyLarge: const TextStyle(color: Colors.grey),
                bodySmall: TextStyle(color: Theme.of(context).canvasColor),
                titleMedium: TextStyle(color: Colors.grey[400]),
                titleSmall: TextStyle(color: Colors.grey[200]),
                bodyMedium: TextStyle(color: Theme.of(context).canvasColor),
                labelSmall: const TextStyle(
                    color: Color.fromARGB(248, 214, 219, 223),
                    fontSize: 14,
                    fontWeight: FontWeight.bold),
                displaySmall: const TextStyle(
                    color: Color.fromARGB(248, 214, 219, 223), fontSize: 14),
              )),
      canvasColor: secondaryColor,
      primaryColor: Colors.black,
      focusColor: Colors.amber[800],
      cardColor: Colors.red.shade800,
      highlightColor: Colors.blueGrey[400],
      secondaryHeaderColor: Colors.teal[800],
      hoverColor: Colors.blueGrey,
      indicatorColor: Colors.pinkAccent[400],
    );
