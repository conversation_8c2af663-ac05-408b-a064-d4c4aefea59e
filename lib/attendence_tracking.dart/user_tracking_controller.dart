import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:schnell_luminator/attendence_tracking.dart/user_tracking_service.dart';
import 'package:schnell_luminator/device_page/device_controller.dart';
import 'package:schnell_luminator/location_selection/location_controller.dart';
import 'package:schnell_luminator/login_page/login_controller.dart';
import 'package:schnell_luminator/utils/dialog_box.dart';
import 'package:schnell_luminator/utils/error_messages.dart';
import 'package:schnell_luminator/utils/session.dart';
import 'package:schnell_luminator/utils/utility.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../s3_bucket/s3_upload_service.dart';

final userTrackingController = ChangeNotifierProvider<UserTrackingProvider>(
    (ref) => UserTrackingProvider());

class UserTrackingProvider extends ChangeNotifier {
  final UserTrackingService _userTrackingService = UserTrackingService();
  final S3UploadService _s3UploadService = S3UploadService();

  Future<void> activityLocationTracking(WidgetRef ref, activityName, entityName,
      {bool isTicket = false, entityType = ''}) async {
    try {
      double latitude = 0.0;
      double longitude = 0.0;
      String clickeventIsFor = ref.read(deviceController).clickeventIsFor;
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      if ((activityName == 'ILM Installation' &&
              clickeventIsFor != "addILM" &&
              clickeventIsFor != "addILMViaPole") ||
          activityName == 'GW Installation') {
        // For ILM and GW installations, the latitude and longitude have already been
        // determined using 20 iterations in advance, so we directly use the fetched values here.
        latitude = double.parse(ref.read(deviceController).fetchedLat);
        longitude = double.parse(ref.read(deviceController).fetchedLong);
      } else {
        final position = await Geolocator.getCurrentPosition(
          locationSettings: AndroidSettings(accuracy: LocationAccuracy.high),
        );
        latitude = position.latitude;
        longitude = position.longitude;
        log("Location Tracking during activity: $latitude, $longitude at $timestamp");
      }
      await storeLocationLocally(timestamp,
          lat: latitude,
          long: longitude,
          locationEnabled: true,
          activityName: activityName ?? '',
          entityName: entityName ?? '',
          entityType: entityType ?? '',
          isTicket: isTicket);
      await Utility.isConnected().then((value) async {
        if (value) {
          //if internet available else location details just stored locally
          await _userTrackingService.pushLocationToUserTelemetry();
        }
      });
    } catch (e) {
      log("Location error: $e");
    }
  }

  Future<void> ppeImageUpload(WidgetRef ref, context, imagePath) async {
    final base64Image = await Utility.compressImage(imagePath);

    int timestamp = DateTime.now().millisecondsSinceEpoch;
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final formattedDate = DateFormat('yyyy-MM-dd').format(dateTime);
    final prefs = await SharedPreferences.getInstance();
    String userId = prefs.getString('userId') ?? '';
    final fileName = '${userId}_$formattedDate.jpg';

    var res = await _s3UploadService.updateSurveyImageServiceS3(
        base64Image, fileName, context,
        isPPE: true);
    if (res == '200') {
      EasyLoading.dismiss();
      await successTick(context, 'Image Uploaded Successfully');
      await ref.read(locationController).getCustomerDetails(ref, context);
    } else if (res == '500' || res == '503') {
      EasyLoading.dismiss();
      ref.read(deviceController).enableButton();
      showSnackBar(ref, context, ErrorMessages.systemNotResponsiveError);
    } else {
      EasyLoading.dismiss();
      ref.read(deviceController).enableButton();
      showSnackBar(ref, context, ErrorMessages.tryAgainError);
    }
  }

  Future<bool> autoLogout(BuildContext context, WidgetRef ref) async {
    var data = await getUser();

    // if already logged in
    if (data != '') {
      //auto logout after 24 hrs
      final prefs = await SharedPreferences.getInstance();
      final loginTimestamp = prefs.getInt('loginTimestamp');
      if (loginTimestamp == null) return false;
      final loginTime = DateTime.fromMillisecondsSinceEpoch(loginTimestamp);
      final now = DateTime.now();
      final difference = now.difference(loginTime);

      if (difference.inHours >= 24) {
        await ref.read(loginController).logout(context, ref);
        return true;
      }
    }
    return false;
  }

  Future<void> initForegroundTask(int interval) async {
    FlutterForegroundTask.init(
        androidNotificationOptions: AndroidNotificationOptions(
            channelId: 'location_tracking',
            channelName: 'Location Tracking',
            channelDescription:
                'This notification appears when background location is running',
            channelImportance: NotificationChannelImportance.MAX,
            priority: NotificationPriority.MAX,
            visibility: NotificationVisibility.VISIBILITY_PUBLIC,
            playSound: true,
            onlyAlertOnce: false,
            enableVibration: true,
            showWhen: true),
        iosNotificationOptions: const IOSNotificationOptions(),
        foregroundTaskOptions: ForegroundTaskOptions(
          // eventAction: ForegroundTaskEventAction.repeat(3600000),// 1 hr
          eventAction:
              ForegroundTaskEventAction.repeat(interval), //in millisecs
          autoRunOnBoot: true,
          autoRunOnMyPackageReplaced: true,
          allowWakeLock: true,
          allowWifiLock: true,
        ));
  }
}
