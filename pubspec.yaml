name: schnell_luminator
description: A new Flutter project.

publish_to: "none"

version: 3.10.3+1

environment:
  sdk: ">=3.1.5 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  dio: ^4.0.6
  path: ^1.8.2

  badges: ^2.0.2
  intl: ^0.19.0
  timeago: ^3.6.1
  camera: ^0.10.5
  latlong2: ^0.9.1
  location: ^6.0.0
  timelines: ^0.1.0
  geocoding: ^2.0.5
  geolocator: ^13.0.1
  flutter_svg: ^2.0.9
  flutter_map: ^7.0.0
  google_fonts: ^4.0.0
  barcode_scan2: ^4.2.1
  in_app_update: ^4.2.2
  toggle_switch: ^2.0.1
  path_provider: ^2.0.11
  auto_size_text: ^3.0.0
  flutter_spinkit: ^5.1.0
  cupertino_icons: ^1.0.2
  flutter_riverpod: ^2.1.1
  wolt_modal_sheet: ^0.4.1
  email_validator: ^2.1.17
  connectivity_plus: ^6.1.3
  searchable_listview: ^2.2.1
  infinite_scroll_pagination: ^3.2.0
  permission_handler: ^11.4.0
  flutter_easyloading: ^3.0.5
  shared_preferences: ^2.0.15
  flutter_map_marker_cluster:
  lecle_bubble_timeline: ^0.0.1+1
  expandable_search_bar: ^0.0.2
  another_flushbar: ^1.12.30
  url_launcher: ^6.1.8
  dropdown_button2: ^2.3.9
  android_intent_plus: ^5.3.0
  installed_apps: ^1.3.1
  flutter_html:
  image_compression: ^1.0.4
  flutter_map_location_marker: ^9.1.0
  flutter_typeahead: ^5.2.0
  flutter_carousel_widget: ^3.0.1
  qr_code_scanner: ^1.0.1
  animated_hint_textfield: ^1.0.1
  firebase_core:
  firebase_remote_config:
  firebase_storage:
  uuid: ^4.4.2
  aws_s3_upload: ^1.5.0
  image: ^4.2.0
  flutter_inappwebview:
  flutter_speed_dial: ^7.0.0
  animated_toggle_switch: ^0.8.4
  lottie: ^2.7.0
  flutter_foreground_task: ^9.1.0
  camerawesome: ^2.3.0
  jwt_decoder: ^2.0.1
  quickalert: ^1.1.0
  crypto: ^3.0.3

dependency_overrides:
  http: ^0.13.3
  permission_handler_android: ^13.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0
  build_runner: ^2.2.1
  mockito: ^5.3.2

flutter:
  uses-material-design: true

  assets:
    - assets/images/