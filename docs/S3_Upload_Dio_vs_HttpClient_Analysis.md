# S3 Upload: <PERSON><PERSON> vs HttpClient Analysis

## Executive Summary

This document explains why **Dio** was causing 403 errors in S3 uploads and why **HttpClient** resolved the issue. The root cause was **data transformation** by <PERSON><PERSON> that invalidated AWS signature verification.

---

## The Problem: 403 Forbidden Errors

### Error Details
- **Error Code**: `XAmzContentSHA256Mismatch`
- **AWS Message**: "The provided 'x-amz-content-sha256' header does not match what was computed"
- **Client Hash**: `9af083e15c1c08f0e824fb598a41e59b2ff0e3d8215b5bd8b9c2dbfded3f6d8c`
- **AWS Hash**: `d4fffbca14458b200c0716e6fbccfd77acf0b8ed6cbcdac6b2c9df8f508bc040`

### Why This Matters
AWS S3 uses **AWS Signature Version 4** which requires:
1. Computing SHA256 hash of request body
2. Including this hash in the signature
3. AWS verifies the actual received data matches the declared hash

---

## Root Cause Analysis

### What Happened with Dio

```dart
// Our code computed hash of original bytes
final bytes = base64Decode(base64Image);
final hash = sha256.convert(bytes).toString(); // Hash A

// Dio internally processed the data
final response = await dio.put(url, data: bytes);
// Dio sent modified data to AWS (Hash B)

// Result: Hash A ≠ Hash B → 403 Error
```

### Dio's Internal Processing
Dio performs several transformations that can modify request data:

1. **Content Encoding**: Automatic gzip compression
2. **Content-Type Processing**: Data serialization based on content type
3. **Interceptors**: Built-in and custom data transformations
4. **Buffer Management**: Internal buffering that may alter byte sequences
5. **Header Normalization**: Automatic header processing

---

## Technical Comparison

### Dio Implementation (Problematic)

```dart
// ❌ PROBLEMATIC APPROACH
static Future<String?> uploadWithDio(Uint8List bytes) async {
  final hash = sha256.convert(bytes).toString();
  
  final response = await dio.put(
    url,
    data: bytes, // Dio may transform this data
    options: Options(
      headers: {
        'x-amz-content-sha256': hash, // Hash of original data
        'Content-Type': 'application/octet-stream',
      },
    ),
  );
  
  // AWS receives different data than what we hashed
  // Result: XAmzContentSHA256Mismatch
}
```

### HttpClient Implementation (Working)

```dart
// ✅ WORKING APPROACH
static Future<String?> uploadWithHttpClient(Uint8List bytes) async {
  final hash = sha256.convert(bytes).toString();
  
  final client = HttpClient();
  final request = await client.putUrl(uri);
  
  // Set headers manually
  request.headers.set('x-amz-content-sha256', hash);
  request.headers.set('Content-Type', 'application/octet-stream');
  
  // Send exact bytes without any transformation
  request.add(bytes); // Raw bytes, no processing
  
  final response = await request.close();
  // AWS receives exactly what we hashed
  // Result: Success
}
```

---

## Key Differences

| Aspect | Dio | HttpClient |
|--------|-----|------------|
| **Data Processing** | Automatic transformations | Raw byte transmission |
| **Content Encoding** | Auto gzip/deflate | Manual control |
| **Header Management** | Automatic normalization | Manual setting |
| **Interceptors** | Built-in processing chain | No automatic processing |
| **Buffer Handling** | Internal buffering/chunking | Direct streaming |
| **AWS Compatibility** | ❌ Problematic for signed requests | ✅ Perfect for signed requests |

---

## Why HttpClient Works Better for AWS

### 1. **Byte-Perfect Transmission**
```dart
// HttpClient sends exactly what you give it
request.add(bytes); // No modification, no encoding, no processing
```

### 2. **Manual Header Control**
```dart
// Complete control over headers
request.headers.set('x-amz-content-sha256', computedHash);
request.headers.set('Content-Length', bytes.length.toString());
// No automatic header addition or modification
```

### 3. **No Automatic Processing**
- No content encoding
- No data serialization
- No interceptor chain
- No automatic compression

### 4. **AWS Signature Compatibility**
AWS signatures are extremely sensitive to:
- Exact byte sequences
- Precise header values
- Content length accuracy
- Hash integrity

HttpClient preserves all of these perfectly.

---

## Performance Implications

### Memory Usage
- **Dio**: Higher memory usage due to internal buffering and processing
- **HttpClient**: Lower memory usage, direct streaming

### Processing Overhead
- **Dio**: Additional CPU cycles for transformations and interceptors
- **HttpClient**: Minimal overhead, direct transmission

### Network Efficiency
- **Dio**: May add compression (good for some cases, bad for pre-processed data)
- **HttpClient**: Sends exactly what you specify

---

## When to Use Each

### Use Dio When:
- Building REST APIs with JSON
- Need automatic retry logic
- Want built-in interceptors
- Working with standard HTTP operations
- Need automatic error handling

### Use HttpClient When:
- AWS S3 uploads with signatures
- Binary data transmission
- Need exact byte control
- Working with signed requests
- Performance-critical applications

---

## Best Practices for S3 Uploads

### 1. **Always Use HttpClient for S3**
```dart
// ✅ Recommended for S3
final client = HttpClient();
final request = await client.putUrl(uri);
```

### 2. **Compute Hash Before Any Processing**
```dart
// ✅ Hash the exact bytes you'll send
final bytes = base64Decode(imageData);
final hash = sha256.convert(bytes).toString();
```

### 3. **Set Headers Manually**
```dart
// ✅ Manual header control
request.headers.set('x-amz-content-sha256', hash);
request.headers.set('Content-Type', contentType);
request.headers.set('Content-Length', bytes.length.toString());
```

### 4. **Use Raw Byte Transmission**
```dart
// ✅ Send exact bytes
request.add(bytes);
```

---

## Conclusion

The 403 errors were caused by **Dio's automatic data processing** which modified the request body after we computed the SHA256 hash for AWS signature verification. This created a mismatch between the declared hash and the actual data received by AWS.

**HttpClient** solved this by providing **raw, unmodified byte transmission**, ensuring that AWS receives exactly the data we computed the hash for.

**Key Takeaway**: For AWS S3 uploads (or any signed HTTP requests), use HttpClient to maintain byte-perfect data integrity required for signature verification.
